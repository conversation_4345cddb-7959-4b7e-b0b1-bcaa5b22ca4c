import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Store, ShoppingBag, Calendar, Star } from 'lucide-react';

const Home = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="text-center py-20 bg-gradient-to-r from-gold-400 to-gold-600 text-white rounded-lg">
        <h1 className="text-5xl font-bold mb-6">Welcome to GoldShop</h1>
        <p className="text-xl mb-8 max-w-2xl mx-auto">
          Discover the finest gold jewelry from trusted shops. Book appointments, browse collections, and find your perfect piece.
        </p>
        {!isAuthenticated ? (
          <div className="space-x-4">
            <Link to="/register" className="bg-white text-gold-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Started
            </Link>
            <Link to="/shops" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gold-600 transition-colors">
              Browse Shops
            </Link>
          </div>
        ) : (
          <Link to="/shops" className="bg-white text-gold-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Browse Shops
          </Link>
        )}
      </section>

      {/* Features Section */}
      <section className="py-16">
        <h2 className="text-3xl font-bold text-center mb-12">Why Choose GoldShop?</h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center p-6">
            <div className="bg-gold-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Store className="w-8 h-8 text-gold-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Trusted Shops</h3>
            <p className="text-gray-600">Browse verified gold shops with authentic jewelry and certified quality.</p>
          </div>
          
          <div className="text-center p-6">
            <div className="bg-gold-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar className="w-8 h-8 text-gold-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Easy Booking</h3>
            <p className="text-gray-600">Schedule appointments with shop owners for personalized consultations.</p>
          </div>
          
          <div className="text-center p-6">
            <div className="bg-gold-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <ShoppingBag className="w-8 h-8 text-gold-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Premium Collection</h3>
            <p className="text-gray-600">Explore a wide range of gold jewelry from 18K to 24K with various designs.</p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-100 rounded-lg p-12 text-center">
        <h2 className="text-3xl font-bold mb-4">Ready to Find Your Perfect Gold Jewelry?</h2>
        <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
          Join thousands of satisfied customers who have found their dream jewelry through our platform.
        </p>
        <div className="space-x-4">
          <Link to="/shops" className="btn-primary">
            Browse Shops
          </Link>
          <Link to="/products" className="btn-secondary">
            View Products
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
