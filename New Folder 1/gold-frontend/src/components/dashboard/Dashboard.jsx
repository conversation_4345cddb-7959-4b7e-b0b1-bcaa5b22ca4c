import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { shopService } from '../../services/shopService';
import { productService } from '../../services/productService';
import { Store, Package, Calendar, Plus, Edit, Trash2 } from 'lucide-react';

const Dashboard = () => {
  const { user } = useAuth();
  const [shops, setShops] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user?.role === 'seller') {
      fetchDashboardData();
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      const [shopsResponse, productsResponse] = await Promise.all([
        shopService.getAllShops(),
        productService.getAllProducts()
      ]);
      
      // Filter shops owned by current user
      const userShops = shopsResponse.data?.filter(shop => shop.owner === user._id) || [];
      setShops(userShops);
      
      // Filter products from user's shops
      const shopIds = userShops.map(shop => shop._id);
      const userProducts = productsResponse.data?.filter(product => 
        shopIds.includes(product.shop._id || product.shop)
      ) || [];
      setProducts(userProducts);
      
    } catch (error) {
      setError('Failed to load dashboard data');
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteShop = async (shopId) => {
    if (window.confirm('Are you sure you want to delete this shop?')) {
      try {
        await shopService.deleteShop(shopId);
        setShops(shops.filter(shop => shop._id !== shopId));
      } catch (error) {
        setError('Failed to delete shop');
      }
    }
  };

  const handleDeleteProduct = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await productService.deleteProduct(productId);
        setProducts(products.filter(product => product._id !== productId));
      } catch (error) {
        setError('Failed to delete product');
      }
    }
  };

  if (user?.role !== 'seller') {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
        <p className="text-gray-600">This dashboard is only available for shop owners.</p>
        <Link to="/" className="btn-primary mt-4">
          Go Home
        </Link>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Shop Owner Dashboard</h1>
        <div className="text-sm text-gray-600">
          Welcome back, {user?.name}
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center">
            <div className="bg-gold-100 p-3 rounded-lg">
              <Store className="w-6 h-6 text-gold-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Shops</p>
              <p className="text-2xl font-bold text-gray-900">{shops.length}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{products.length}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <Calendar className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Bookings</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </div>
      </div>

      {/* Shops Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900">My Shops</h2>
          <Link to="/dashboard/shop/new" className="btn-primary flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Add Shop
          </Link>
        </div>

        {shops.length === 0 ? (
          <div className="card text-center py-8">
            <Store className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No shops yet</h3>
            <p className="text-gray-600 mb-4">Create your first shop to start selling.</p>
            <Link to="/dashboard/shop/new" className="btn-primary">
              Create Shop
            </Link>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 gap-4">
            {shops.map((shop) => (
              <div key={shop._id} className="card">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{shop.name}</h3>
                    <p className="text-sm text-gray-600">{shop.area}, {shop.city}</p>
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      to={`/dashboard/shop/${shop._id}/edit`}
                      className="p-2 text-gold-600 hover:bg-gold-50 rounded"
                    >
                      <Edit className="w-4 h-4" />
                    </Link>
                    <button
                      onClick={() => handleDeleteShop(shop._id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    shop.subscriptionPlan === 'Gold' ? 'bg-gold-100 text-gold-800' :
                    shop.subscriptionPlan === 'Premium' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {shop.subscriptionPlan}
                  </span>
                  
                  <Link
                    to={`/dashboard/shop/${shop._id}/products`}
                    className="text-sm text-gold-600 hover:text-gold-700"
                  >
                    Manage Products →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recent Products */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900">Recent Products</h2>
          <Link to="/dashboard/product/new" className="btn-primary flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Add Product
          </Link>
        </div>

        {products.length === 0 ? (
          <div className="card text-center py-8">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products yet</h3>
            <p className="text-gray-600 mb-4">Add products to your shops to start selling.</p>
            <Link to="/dashboard/product/new" className="btn-primary">
              Add Product
            </Link>
          </div>
        ) : (
          <div className="grid md:grid-cols-3 gap-4">
            {products.slice(0, 6).map((product) => (
              <div key={product._id} className="card">
                {product.images_urls && product.images_urls[0] && (
                  <img 
                    src={product.images_urls[0]} 
                    alt={product.title}
                    className="w-full h-32 object-cover rounded-lg mb-3"
                  />
                )}
                
                <div className="space-y-2">
                  <h3 className="font-semibold text-gray-900 truncate">{product.title}</h3>
                  <div className="flex justify-between items-center">
                    <span className="text-gold-600 font-bold">
                      ${parseFloat(product.price?.$numberDecimal || product.price).toLocaleString()}
                    </span>
                    <span className="text-sm text-gray-500">{product.karat}</span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Link
                      to={`/dashboard/product/${product._id}/edit`}
                      className="flex-1 text-center py-1 text-sm text-gold-600 hover:bg-gold-50 rounded"
                    >
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteProduct(product._id)}
                      className="flex-1 text-center py-1 text-sm text-red-600 hover:bg-red-50 rounded"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
