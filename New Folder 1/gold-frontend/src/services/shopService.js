import api from './api';

export const shopService = {
  // Create new shop
  createShop: async (shopData) => {
    const response = await api.post('/shop/create', shopData);
    return response.data;
  },

  // Get all shops
  getAllShops: async () => {
    const response = await api.get('/shop');
    return response.data;
  },

  // Get single shop by ID
  getShop: async (shopId) => {
    const response = await api.get(`/shop/${shopId}`);
    return response.data;
  },

  // Update shop
  updateShop: async (shopId, shopData) => {
    const response = await api.put(`/shop/${shopId}`, shopData);
    return response.data;
  },

  // Delete shop
  deleteShop: async (shopId) => {
    const response = await api.delete(`/shop/${shopId}`);
    return response.data;
  },
};
