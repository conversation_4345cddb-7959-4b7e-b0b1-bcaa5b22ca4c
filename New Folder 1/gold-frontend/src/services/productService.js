import api from './api';

export const productService = {
  // Create new product
  createProduct: async (productData) => {
    const response = await api.post('/product/create', productData);
    return response.data;
  },

  // Get all products
  getAllProducts: async () => {
    const response = await api.get('/product');
    return response.data;
  },

  // Get single product by ID
  getProduct: async (productId) => {
    const response = await api.get(`/product/${productId}`);
    return response.data;
  },

  // Update product
  updateProduct: async (productId, productData) => {
    const response = await api.put(`/product/${productId}`, productData);
    return response.data;
  },

  // Delete product
  deleteProduct: async (productId) => {
    const response = await api.delete(`/product/${productId}`);
    return response.data;
  },
};
