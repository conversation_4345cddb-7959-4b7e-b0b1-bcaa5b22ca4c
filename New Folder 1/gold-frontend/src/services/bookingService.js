import api from './api';

export const bookingService = {
  // Add available time slot (shop owner)
  addAvailableTime: async (timeData) => {
    const response = await api.post('/booking', timeData);
    return response.data;
  },

  // Get available times for a shop
  getAvailableTimesForShop: async (shopId) => {
    const response = await api.get(`/booking/${shopId}`);
    return response.data;
  },

  // Book a time slot
  bookTime: async (timeId) => {
    const response = await api.post('/booking/book', { timeId });
    return response.data;
  },
};
