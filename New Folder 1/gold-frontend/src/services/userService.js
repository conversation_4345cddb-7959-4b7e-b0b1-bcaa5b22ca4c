import api from './api';

export const userService = {
  // Get current user profile
  getProfile: async () => {
    const response = await api.get('/user/me');
    return response.data;
  },

  // Update user profile
  updateProfile: async (userData) => {
    const response = await api.put('/user', userData);
    return response.data;
  },

  // Delete user account
  deleteAccount: async () => {
    const response = await api.delete('/user');
    return response.data;
  },

  // Reset password (authenticated user)
  resetPassword: async (passwordData) => {
    const response = await api.post('/user/reset_password', passwordData);
    return response.data;
  },

  // Forgot password (send reset email)
  forgotPassword: async (email) => {
    const response = await api.post('/user/forgot-password', { email });
    return response.data;
  },

  // Reset password with token
  resetPasswordWithToken: async (token, newPassword) => {
    const response = await api.post(`/user/reset-password/${token}`, { password: newPassword });
    return response.data;
  },
};
