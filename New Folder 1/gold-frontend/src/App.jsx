import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import Layout from './components/layout/Layout';

// Pages
import Home from './pages/Home';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import ShopList from './components/shop/ShopList';
import ShopForm from './components/shop/ShopForm';
import ProductList from './components/product/ProductList';
import ProductForm from './components/product/ProductForm';
import BookingForm from './components/booking/BookingForm';
import BookingList from './components/booking/BookingList';
import UserProfile from './components/user/UserProfile';
import Dashboard from './components/dashboard/Dashboard';

// Protected Route Component
const ProtectedRoute = ({ children, requireAuth = true, requireRole = null }) => {
  const { isAuthenticated, user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-600"></div>
      </div>
    );
  }

  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requireRole && user?.role !== requireRole) {
    return <Navigate to="/" replace />;
  }

  return children;
};

function AppContent() {
  return (
    <Router>
      <Layout>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/shops" element={<ShopList />} />
          <Route path="/products" element={<ProductList />} />

          {/* Protected Routes */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <UserProfile />
              </ProtectedRoute>
            }
          />
          <Route
            path="/bookings"
            element={
              <ProtectedRoute>
                <BookingList />
              </ProtectedRoute>
            }
          />
          <Route
            path="/shops/:shopId/book"
            element={
              <ProtectedRoute>
                <BookingForm />
              </ProtectedRoute>
            }
          />

          {/* Seller Routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute requireRole="seller">
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dashboard/shop/new"
            element={
              <ProtectedRoute requireRole="seller">
                <ShopForm />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dashboard/product/new"
            element={
              <ProtectedRoute requireRole="seller">
                <ProductForm />
              </ProtectedRoute>
            }
          />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Layout>
    </Router>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
