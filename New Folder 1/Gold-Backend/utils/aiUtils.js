import OpenAI from "openai";
import dotenv from "dotenv";
dotenv.config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const generateDescription = async ({
  type,
  weight,
  karat,
  occasion,
}) => {
  const prompt = `اكتب وصف تسويقي جذاب لقطعة ذهب نوعها: ${type}، عيارها ${karat}، وزنها ${weight} جرام، مناسبة لـ ${occasion}. اجعله احترافي وجذاب.`;

  const response = await openai.chat.completions.create({
    model: "gpt-3.5-turbo", // ممكن تستخدم "gpt-4" لو متاح
    messages: [{ role: "user", content: prompt }],
    temperature: 0.7,
  });

  return response.choices[0].message.content.trim();
};
